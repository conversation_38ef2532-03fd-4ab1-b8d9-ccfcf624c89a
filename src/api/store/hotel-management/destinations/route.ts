import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
  const { limit = 20, offset = 0, is_featured, is_active } = req.query || {};
  const filters: Record<string, any> = {};

  if (is_featured !== undefined) {
    filters.is_featured = is_featured === "true";
  }
  if (is_active !== undefined) {
    filters.is_active = is_active === "true";
  }
  const {
    data: destinations,
    metadata: { count, take, skip },
  } = await query.graph({
    filters,
    entity: "destination",
    fields: ["*", "images.*", ...(req.validatedQuery?.fields.split(",") || [])],
    pagination: {
      skip: Number(offset),
      take: Number(limit),
    },
  });
  res.json({
    destinations,
    count,
    limit: take,
    offset: skip,
  });
};
