import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve("query");
  console.log(req.params.id);

  const { data: destination } = await query.graph({
    entity: "destination",
    filters: {
      handle: req.params.id,
    },
    fields: ["*", "images.*"],
  });
  res.json({ destination });
};
