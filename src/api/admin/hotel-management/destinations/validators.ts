import { z } from "zod";

export const PostAdminCreateDestination = z.object({
  name: z.string(),
  handle: z.string(),
  description: z.string().optional(),
  is_active: z.boolean().default(true),
  country: z.string(),
  currency: z.string(),
  location: z.string().optional(),
  tags: z.array(z.string()).optional(),
  website: z.string().optional(),
  is_featured: z.boolean().default(false),
});

export const PostAdminUpdateDestination = z.object({
  id: z.string(),
  name: z.string().optional(),
  handle: z.string().optional(),
  description: z.string().optional(),
  is_active: z.boolean().default(true),
  country: z.string().optional(),
  currency: z.string().optional(),
  location: z.string().optional(),
  tags: z.array(z.string()).optional(),
  website: z.string().optional(),
  is_featured: z.boolean().default(false),
});

export const PostAdminDeleteDestination = z.object({
  ids: z.string(),
});
