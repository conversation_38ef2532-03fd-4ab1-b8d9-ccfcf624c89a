import {
  StepResponse,
  WorkflowResponse,
  createStep,
  createWorkflow,
} from "@camped-ai/framework/workflows-sdk";
import DestinationModuleService from "src/modules/hotel-management/destination/service";
import { DESTINATION_MODULE } from "src/modules/hotel-management/destination";
import { IProductModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import { emitEventStep } from "@camped-ai/medusa/core-flows";

export type UpdateDestinationStepInput = {
  id: string;
  name?: string;
  handle?: string;
  description?: string;
  is_active?: boolean;
  country?: string;
  currency?: string;
  location?: string;
  is_featured?: boolean;
  tags?: string[] | string;
  website?: string;
};

type UpdateDestinationWorkflowInput = UpdateDestinationStepInput;

export const updateDestinationStep = createStep(
  "update-destination-step",
  async (input: UpdateDestinationStepInput, { container }) => {
    const destinationModuleService: DestinationModuleService =
      container.resolve(DESTINATION_MODULE);
    const productModuleService: IProductModuleService = container.resolve(
      Modules.PRODUCT
    );

    const prevUpdatedDestination =
      await destinationModuleService.retrieveDestination(input.id);

    const updateData = { ...input };
    if (input.tags) {
      updateData.tags = JSON.stringify(input.tags);
    }

    const updateDestination = await destinationModuleService.updateDestinations(
      updateData
    );
    const filteredCategoryUpdate = Object.fromEntries(
      Object.entries({
        name: input.name,
        handle: input.handle,
        description: input.description,
        is_active: input.is_active,
      }).filter(([_, value]) => value !== undefined)
    );
    await productModuleService.updateProductCategories(
      prevUpdatedDestination.category_id,
      filteredCategoryUpdate
    );

    return new StepResponse(updateDestination, prevUpdatedDestination);
  },
  async (prevUpdatedDestination, { container }) => {
    if (!prevUpdatedDestination) {
      return;
    }
    const destinationModuleService: DestinationModuleService =
      container.resolve(DESTINATION_MODULE);

    await destinationModuleService.updateDestinations(prevUpdatedDestination);
  }
);

export const UpdateDestinationWorkflow = createWorkflow(
  "update-destination",
  (input: UpdateDestinationWorkflowInput) => {
    // First, execute the update step
    const destination = updateDestinationStep(input);

    // Collect all changes in a single event
    const changes = [];

    // Check for active status change
    if (input.is_active !== undefined) {
      changes.push({
        type: "active_status",
        new_status: input.is_active
      });
    }

    // Check for featured status change
    if (input.is_featured !== undefined) {
      changes.push({
        type: "featured_status",
        new_status: input.is_featured
      });
    }

    // Check for currency change
    if (input.currency !== undefined) {
      changes.push({
        type: "currency",
        new_value: input.currency
      });
    }

    // Only emit event if there are changes
    if (changes.length > 0) {
      emitEventStep({
        eventName: "destination.status_changed",
        data: {
          id: input.id,
          destination_name: destination.name,
          changes: changes
        },
      });
    }

    return new WorkflowResponse(destination);
  }
);
