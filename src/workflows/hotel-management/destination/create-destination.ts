import {
  StepResponse,
  WorkflowResponse,
  createStep,
  createWorkflow,
} from "@camped-ai/framework/workflows-sdk";
import DestinationModuleService from "src/modules/hotel-management/destination/service";
import { DESTINATION_MODULE } from "src/modules/hotel-management/destination";
import { IProductModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import { emitEventStep } from "@camped-ai/medusa/core-flows";

export type CreateDestinationStepInput = {
  name: string;
  handle: string;
  description?: string;
  is_active?: boolean;
  country: string;
  currency: string;
  location?: string;
  tags?: string[] | string;
  is_featured?: boolean;
  website?: string;
};

type CreateDestinationWorkflowInput = CreateDestinationStepInput;

export const createDestinationStep = createStep(
  "create-destination-step",
  async (input: CreateDestinationStepInput, { container }) => {
    const destinationModuleService: DestinationModuleService =
      container.resolve(DESTINATION_MODULE);
    const productModuleService: IProductModuleService = container.resolve(
      Modules.PRODUCT
    );
    const category = await productModuleService.createProductCategories({
      name: input.name,
      handle: input.handle,
      description: input?.description,
      is_active: input?.is_active || true,
    });

    const destination = await destinationModuleService.createDestinations({
      ...input,
      category_id: category.id,
      tags: input.tags ? input.tags : null,
    });

    return new StepResponse(destination, destination.id);
  },
  async (ids: string[], { container }) => {
    const destinationModuleService: DestinationModuleService =
      container.resolve(DESTINATION_MODULE);
    await destinationModuleService.deleteDestinations(ids);
  }
);

export const CreateDestinationWorkflow = createWorkflow(
  "create-destination",
  (input: CreateDestinationWorkflowInput) => {
    // First, create the destination
    const destination = createDestinationStep(input);

    // Now we can emit the event with the input data
    emitEventStep({
      eventName: "destination.created",
      data: {
        // We don't have the ID yet since it's a new destination
        destination: destination.id,
        name: input.name,
        country: input.country,
        is_active: input.is_active || true,
      },
    });

    return new WorkflowResponse(destination);
  }
);
