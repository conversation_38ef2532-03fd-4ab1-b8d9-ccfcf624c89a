// Add this interface to your types
export interface DestinationFaqData {
  id: string;
  question: string;
  answer: string;
  destination_id: string;
  created_at: string;
  updated_at: string;
}

export interface DestinationData {
  id: string;
  name: string;
  handle: string;
  description: string | null;
  is_active: boolean;
  is_featured: boolean;
  country: string;
  currency: string;
  location: string | null;
  tags: string[] | string | Record<string, any> | null;
  website: string | null;
  category_id?: string;
  faqs?: DestinationFaqData[];
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export type CancellationPolicyData = {
  id: string;
  name: string;
  description?: string;
  hotel_id: string;
  days_before_checkin: number;
  refund_type: "percentage" | "fixed" | "no_refund";
  refund_amount: number;
  is_active: boolean;
  metadata?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
};

export type HotelData = {
  id: string;
  name: string;
  handle: string;
  description: string;
  is_active: boolean;
  website: string | null;
  email: string | null;
  destination_id: string;
  rating?: number;
  total_reviews?: number;
  notes?: string;
  location?: string;
  address?: string;
  phone_number?: string;
  timezone?: string;
  available_languages?: string[];
  tax_type?: string;
  tax_number?: string;
  tags?: string[];
  amenities?: string[];
  rules?: string[];
  safety_measures?: string[];
  currency?: string;
  is_featured?: boolean;
  check_in_time?: string;
  check_out_time?: string;
  is_pets_allowed?: boolean;
  parent_category_id?: string | null;
  category_id?: string;
  images?: Array<{
    id?: string;
    url: string;
    isThumbnail?: boolean;
  }>;
  cancellation_policies?: CancellationPolicyData[];
  created_at?: string;
  updated_at?: string;
  deleted_at?: string | null;
  room_types?: string[];
};

export type RoomTypeData = {
  id: string;
  name: string;
  handle: string;
  description: string;
  is_active: boolean;
  is_internal: boolean;
  category_id?: string;
  created_at?: string;
  updated_at?: string;
  deleted_at?: string | null;
};

import { OrderDTO, CustomerDTO } from "@camped-ai/framework/types";

export enum SubscriptionStatus {
  ACTIVE = "active",
  CANCELED = "canceled",
  EXPIRED = "expired",
  FAILED = "failed",
}

export enum SubscriptionInterval {
  DAILY = "daily",
  WEEKLY = "weekly",
  MONTHLY = "monthly",
  YEARLY = "yearly",
}

export type SubscriptionData = {
  id: string;
  status: SubscriptionStatus;
  interval: SubscriptionInterval;
  subscription_date: string;
  last_order_date: string;
  next_order_date: string | null;
  expiration_date: string;
  metadata: Record<string, unknown> | null;
  orders?: OrderDTO[];
  customer?: CustomerDTO;
};
