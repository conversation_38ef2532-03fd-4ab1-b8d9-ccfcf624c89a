import {
  Button,
  Input,
  FocusModal,
  Text,
  Label,
  Switch,
  Heading,
  Textarea,
  Tabs,
  Select,
  Tooltip,
  IconButton,
} from "@camped-ai/ui";
import { useForm } from "react-hook-form";
import DestinationMediaSection from "./destination/destination-media-section";
import { MediaField } from "./hotel/media-item";
import { useState } from "react";
import { Info, Globe, MapPin, DollarSign, Tag, Building } from "lucide-react";

export type DestinationFormData = {
  name: string;
  handle: string;
  description: string;
  is_active: boolean;
  is_featured: boolean;
  country: string;
  currency: string;
  location: string | null;
  tags: string[] | null;
  website?: string | null;
  category_id?: string;
  media?: MediaField[];
  image_ids?: string[];
  thumbnail_image_id?: string;
  id?: string;
  faqs?: Array<{
    id?: string;
    question: string;
    answer: string;
  }>;
};

type DestinationFormProps = {
  formData: DestinationFormData;
  setFormData: (data: DestinationFormData) => void;
  onSubmit: () => Promise<boolean>;
  isEdit?: boolean;
  closeModal: () => void;
};

const DestinationForm = ({
  formData,
  setFormData,
  onSubmit,
  isEdit,
  closeModal,
}: DestinationFormProps) => {
  const form = useForm<DestinationFormData>({
    defaultValues: formData,
  });

  const [activeTab, setActiveTab] = useState("basics");
  const [tagsInput, setTagsInput] = useState(
    Array.isArray(formData.tags) ? formData.tags.join(", ") : ""
  );

  // Common currency options
  const currencyOptions = [
    { label: "USD - US Dollar", value: "USD" },
    { label: "EUR - Euro", value: "EUR" },
    { label: "GBP - British Pound", value: "GBP" },
    { label: "JPY - Japanese Yen", value: "JPY" },
    { label: "CHF - Swiss Franc", value: "CHF" },
    { label: "CAD - Canadian Dollar", value: "CAD" },
    { label: "AUD - Australian Dollar", value: "AUD" },
    { label: "CNY - Chinese Yuan", value: "CNY" },
    { label: "INR - Indian Rupee", value: "INR" },
  ];

  // Popular country options
  const countryOptions = [
    { label: "United States", value: "United States" },
    { label: "United Kingdom", value: "United Kingdom" },
    { label: "France", value: "France" },
    { label: "Italy", value: "Italy" },
    { label: "Spain", value: "Spain" },
    { label: "Germany", value: "Germany" },
    { label: "Japan", value: "Japan" },
    { label: "Australia", value: "Australia" },
    { label: "Canada", value: "Canada" },
    { label: "Switzerland", value: "Switzerland" },
    { label: "Thailand", value: "Thailand" },
    { label: "Greece", value: "Greece" },
    { label: "Mexico", value: "Mexico" },
    { label: "Brazil", value: "Brazil" },
    { label: "India", value: "India" },
    { label: "China", value: "China" },
  ];

  // Handle tags input change
  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTagsInput(e.target.value);
    // Convert comma-separated string to array
    const tagsArray = e.target.value.split(",").map(tag => tag.trim()).filter(tag => tag);
    setFormData({ ...formData, tags: tagsArray });
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Get the form values including media
    const formValues = form.getValues();

    // Ensure tags is properly formatted
    const tagsArray = tagsInput.split(",").map(tag => tag.trim()).filter(tag => tag);

    // Update the formData with the form values
    setFormData({
      ...formData,
      ...formValues,
      tags: tagsArray,
      is_featured: formValues.is_featured || false,
    });

    const success = await onSubmit();
    if (success) {
      closeModal();
    }
  };

  return (
    <FocusModal.Content>
      <FocusModal.Header>
        <div className="flex justify-between items-center">
          <Button
            variant="primary"
            onClick={handleSubmit}
            disabled={!formData.name || !formData.handle}
          >
            {isEdit ? "Update" : "Save"}
          </Button>
        </div>
      </FocusModal.Header>
      <FocusModal.Body className="flex flex-col items-center py-16 gap-4 overflow-y-auto">
        <Heading level="h1">
          {isEdit ? "Edit Destination" : "Create Destination"}
        </Heading>
        <Tabs defaultValue="general" className="w-full max-w-lg">
          <Tabs.List>
            <Tabs.Trigger value="general">General</Tabs.Trigger>
            <Tabs.Trigger value="media">Media</Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content value="general" className="flex w-full flex-col gap-y-6 pt-5">
          <div>
            <Text className="mb-2 flex items-center gap-1">
              Destination Name{" "}
              <span className="text-red-500 text-[0.6em]">★</span>
            </Text>
            <Input
              value={formData.name}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  name: e.target.value,
                  handle: e.target.value.toLowerCase().replace(/\\s+/g, "-"),
                })
              }
              placeholder="Enter destination name"
            />
          </div>
          <div>
            <Text className="mb-2">Description</Text>
            <Textarea
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              placeholder="Enter description"
            />
          </div>
          <div className="flex gap-x-8">
            <div className="flex items-center gap-x-2">
              <Switch
                id="is-active"
                checked={formData.is_active}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, is_active: checked })
                }
              />
              <Label htmlFor="is-active">Active</Label>
            </div>
            <div className="flex items-center gap-x-2">
              <Switch
                id="is-featured"
                checked={formData.is_featured}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, is_featured: checked })
                }
              />
              <Label htmlFor="is-featured">Featured</Label>
            </div>
          </div>
          <div>
            <Text className="mb-2 flex items-center gap-1">
              Handle <span className="text-red-500 text-[0.6em]">★</span>
            </Text>
            <Input
              value={formData.handle}
              onChange={(e) =>
                setFormData({ ...formData, handle: e.target.value })
              }
              placeholder="Enter handle"
            />
          </div>
          <div>
            <Text className="mb-2 flex items-center gap-1">
              Country <span className="text-red-500 text-[0.6em]">★</span>
            </Text>
            <Input
              value={formData.country}
              onChange={(e) =>
                setFormData({ ...formData, country: e.target.value })
              }
              placeholder="Enter country"
            />
          </div>
          <div>
            <Text className="mb-2 flex items-center gap-1">
              Currency <span className="text-red-500 text-[0.6em]">★</span>
            </Text>
            <Input
              value={formData.currency}
              onChange={(e) =>
                setFormData({ ...formData, currency: e.target.value })
              }
              placeholder="Enter currency"
            />
          </div>
          <div>
            <Text className="mb-2">Location</Text>
            <Input
              value={formData.location || ""}
              onChange={(e) =>
                setFormData({ ...formData, location: e.target.value })
              }
              placeholder="Enter location"
            />
          </div>
          <div>
            <Text className="mb-2">Website</Text>
            <Input
              value={formData.website || ""}
              onChange={(e) =>
                setFormData({ ...formData, website: e.target.value })
              }
              placeholder="Enter website URL"
            />
          </div>
          </Tabs.Content>
          <Tabs.Content value="media" className="pt-5">
            <DestinationMediaSection form={form} />
          </Tabs.Content>
        </Tabs>
      </FocusModal.Body>
    </FocusModal.Content>
  );
};

export default DestinationForm;
