import React, { useState } from "react";
import { Button, Input, Textarea, Text, IconButton } from "@camped-ai/ui";
import { Plus, Trash2, Edit3 } from "lucide-react";
import { UseFormReturn } from "react-hook-form";

export type DestinationFormData = {
  name: string;
  handle: string;
  description: string;
  is_active: boolean;
  is_featured: boolean;
  country: string;
  currency: string;
  location: string | null;
  tags: string[] | null;
  website?: string | null;
  category_id?: string;
  media?: any[];
  image_ids?: string[];
  thumbnail_image_id?: string;
  id?: string;
  customCountry?: string;
  customCurrency?: string;
  faqs?: Array<{
    id?: string;
    question: string;
    answer: string;
  }>;
};

interface DestinationFaqSectionProps {
  form: UseFormReturn<DestinationFormData>;
}

const DestinationFaqSection: React.FC<DestinationFaqSectionProps> = ({ form }) => {
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [newFaq, setNewFaq] = useState({ question: "", answer: "" });

  const faqs = form.watch("faqs") || [];

  const addFaq = () => {
    if (newFaq.question.trim() && newFaq.answer.trim()) {
      const currentFaqs = form.getValues("faqs") || [];
      form.setValue("faqs", [...currentFaqs, { ...newFaq }]);
      setNewFaq({ question: "", answer: "" });
    }
  };

  const updateFaq = (index: number, updatedFaq: { question: string; answer: string }) => {
    const currentFaqs = form.getValues("faqs") || [];
    const updatedFaqs = [...currentFaqs];
    updatedFaqs[index] = { ...updatedFaqs[index], ...updatedFaq };
    form.setValue("faqs", updatedFaqs);
    setEditingIndex(null);
  };

  const deleteFaq = (index: number) => {
    const currentFaqs = form.getValues("faqs") || [];
    const updatedFaqs = currentFaqs.filter((_, i) => i !== index);
    form.setValue("faqs", updatedFaqs);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Text className="text-lg font-medium">Frequently Asked Questions</Text>
      </div>

      {/* Existing FAQs */}
      <div className="space-y-3">
        {faqs.map((faq, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
            {editingIndex === index ? (
              <EditFaqForm
                faq={faq}
                onSave={(updatedFaq) => updateFaq(index, updatedFaq)}
                onCancel={() => setEditingIndex(null)}
              />
            ) : (
              <div className="space-y-2">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <Text className="font-medium text-gray-900">{faq.question}</Text>
                    <Text className="text-gray-600 mt-1">{faq.answer}</Text>
                  </div>
                  <div className="flex gap-2 ml-4">
                    <IconButton
                      size="small"
                      variant="transparent"
                      onClick={() => setEditingIndex(index)}
                    >
                      <Edit3 className="w-4 h-4" />
                    </IconButton>
                    <IconButton
                      size="small"
                      variant="transparent"
                      onClick={() => deleteFaq(index)}
                    >
                      <Trash2 className="w-4 h-4 text-red-500" />
                    </IconButton>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Add new FAQ */}
      <div className="border border-gray-200 rounded-lg p-4">
        <Text className="font-medium mb-3">Add New FAQ</Text>
        <div className="space-y-3">
          <div>
            <Text className="text-sm text-gray-600 mb-1">Question</Text>
            <Input
              value={newFaq.question}
              onChange={(e) => setNewFaq({ ...newFaq, question: e.target.value })}
              placeholder="Enter the question"
            />
          </div>
          <div>
            <Text className="text-sm text-gray-600 mb-1">Answer</Text>
            <Textarea
              value={newFaq.answer}
              onChange={(e) => setNewFaq({ ...newFaq, answer: e.target.value })}
              placeholder="Enter the answer"
              rows={3}
            />
          </div>
          <Button
            variant="secondary"
            size="small"
            onClick={addFaq}
            disabled={!newFaq.question.trim() || !newFaq.answer.trim()}
          >
            <Plus className="w-4 h-4 mr-1" />
            Add FAQ
          </Button>
        </div>
      </div>
    </div>
  );
};

interface EditFaqFormProps {
  faq: { question: string; answer: string };
  onSave: (faq: { question: string; answer: string }) => void;
  onCancel: () => void;
}

const EditFaqForm: React.FC<EditFaqFormProps> = ({ faq, onSave, onCancel }) => {
  const [editedFaq, setEditedFaq] = useState(faq);

  const handleSave = () => {
    if (editedFaq.question.trim() && editedFaq.answer.trim()) {
      onSave(editedFaq);
    }
  };

  return (
    <div className="space-y-3">
      <div>
        <Text className="text-sm text-gray-600 mb-1">Question</Text>
        <Input
          value={editedFaq.question}
          onChange={(e) => setEditedFaq({ ...editedFaq, question: e.target.value })}
          placeholder="Enter the question"
        />
      </div>
      <div>
        <Text className="text-sm text-gray-600 mb-1">Answer</Text>
        <Textarea
          value={editedFaq.answer}
          onChange={(e) => setEditedFaq({ ...editedFaq, answer: e.target.value })}
          placeholder="Enter the answer"
          rows={3}
        />
      </div>
      <div className="flex gap-2">
        <Button
          variant="primary"
          size="small"
          onClick={handleSave}
          disabled={!editedFaq.question.trim() || !editedFaq.answer.trim()}
        >
          Save
        </Button>
        <Button variant="secondary" size="small" onClick={onCancel}>
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default DestinationFaqSection;
