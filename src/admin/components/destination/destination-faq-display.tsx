import React, { useState } from "react";
import { Text, Heading } from "@camped-ai/ui";
import { ChevronDown, ChevronUp } from "lucide-react";
import { DestinationFaqData } from "../../types";

interface DestinationFaqDisplayProps {
  faqs: DestinationFaqData[];
}

const DestinationFaqDisplay: React.FC<DestinationFaqDisplayProps> = ({ faqs }) => {
  const [expandedFaq, setExpandedFaq] = useState<string | null>(null);

  if (!faqs || faqs.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <Heading level="h3" className="text-lg font-medium mb-4">
          Frequently Asked Questions
        </Heading>
        <Text className="text-gray-500">No FAQs available for this destination.</Text>
      </div>
    );
  }

  const toggleFaq = (faqId: string) => {
    setExpandedFaq(expandedFaq === faqId ? null : faqId);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <Heading level="h3" className="text-lg font-medium mb-4">
        Frequently Asked Questions
      </Heading>
      
      <div className="space-y-3">
        {faqs.map((faq) => (
          <div key={faq.id} className="border border-gray-200 rounded-lg">
            <button
              onClick={() => toggleFaq(faq.id)}
              className="w-full px-4 py-3 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
            >
              <Text className="font-medium text-gray-900">{faq.question}</Text>
              {expandedFaq === faq.id ? (
                <ChevronUp className="w-5 h-5 text-gray-500" />
              ) : (
                <ChevronDown className="w-5 h-5 text-gray-500" />
              )}
            </button>
            
            {expandedFaq === faq.id && (
              <div className="px-4 pb-3 border-t border-gray-100">
                <Text className="text-gray-600 mt-2">{faq.answer}</Text>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default DestinationFaqDisplay;
