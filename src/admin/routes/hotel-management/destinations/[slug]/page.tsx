import { Container, <PERSON>ing, Text, Badge, Button } from "@camped-ai/ui";
import { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { DestinationData, HotelData } from "../../../../types";
import DestinationFormModern from "../../../../components/destination-form-modern";
import { DestinationFormData } from "../../../../components/destination-form";
import { FocusModal, toast, Toaster } from "@camped-ai/ui";
import "./modal-fix.css"; // Import custom CSS to fix z-index issues
import Prompt from "../../../../components/prompt";
import DestinationMetafieldsWidget from "../../../../widgets/destination-metafields";
import HideSidebarItemsWidget from "../../../../widgets/hide-sidebar-items-widget";
import { MediaField } from "../../../../components/hotel/media-item";
import { PlusMini, Buildings, ChevronLeft, MagnifyingGlass, Tag } from "@camped-ai/icons";
import { Globe, MapPin, Star, DollarSign, Image, Clock, Edit, Trash2, Hotel, Bookmark, Flag, Hash, FileText, CheckCircle, XCircle, Filter, Bed } from "lucide-react";

const DestinationDetailPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [destination, setDestination] = useState<DestinationData | null>(null);
  const [destinationImages, setDestinationImages] = useState<any[]>([]);
  const [hotels, setHotels] = useState<HotelData[]>([]);
  const [isLoadingHotels, setIsLoadingHotels] = useState(false);
  const [formData, setFormData] = useState<DestinationFormData>({
    name: "",
    handle: "",
    description: "",
    is_active: true,
    is_featured: false,
    country: "",
    currency: "",
    location: null,
    tags: null,
    website: null,
    category_id: "",
    media: [],
  });
  const [isLoading, setIsLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [deleteOpen, setDeleteOpen] = useState(false);
  // State for the page

  // Function to fetch hotels for this destination
  const fetchHotelsForDestination = async (destinationId: string) => {
    setIsLoadingHotels(true);
    try {
      const response = await fetch(`/admin/hotel-management/hotels?limit=100`, {
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Failed to fetch hotels");
      }

      const data = await response.json();

      // Filter hotels by destination_id
      const destinationHotels = data.hotels.filter(
        (hotel: HotelData) => hotel.destination_id === destinationId
      );

      // Fetch images for each hotel
      const hotelsWithImages = await Promise.all(
        destinationHotels.map(async (hotel: HotelData) => {
          try {
            const imagesResponse = await fetch(
              `/admin/hotel-management/hotels/${hotel.id}/images`,
              { credentials: "include" }
            );

            if (imagesResponse.ok) {
              const imagesData = await imagesResponse.json();

              if (imagesData.images && imagesData.images.length > 0) {
                return {
                  ...hotel,
                  images: imagesData.images
                };
              }
            }

            return hotel;
          } catch (error) {
            console.error(`Error fetching images for hotel ${hotel.id}:`, error);
            return hotel;
          }
        })
      );

      setHotels(hotelsWithImages);
    } catch (error) {
      console.error("Error fetching hotels:", error);
      toast.error("Error", {
        description: "Failed to load hotels for this destination",
      });
    } finally {
      setIsLoadingHotels(false);
    }
  };

  // Fetch destination data and images
  useEffect(() => {
    const fetchDestinationData = async () => {
      try {
        // Fetch destination data
        const response = await fetch(`/admin/hotel-management/destinations/${slug}`, {
          credentials: "include",
        });
        const data = await response.json();

        const destinationData = Array.isArray(data?.destination)
          ? data?.destination[0]
          : data.destination;

        if (destinationData) {
          setDestination(destinationData);

          // Fetch hotels for this destination
          fetchHotelsForDestination(destinationData.id);

          // Fetch destination images
          if (destinationData.id) {
            try {
              const imagesResponse = await fetch(
                `/admin/hotel-management/destinations/${destinationData.id}/images`,
                { credentials: "include" }
              );
              const imagesData = await imagesResponse.json();

              if (imagesData.images) {
                setDestinationImages(imagesData.images);

                // Convert images to media format for the form
                const mediaItems: MediaField[] = imagesData.images.map((img: any) => ({
                  id: img.id,
                  url: img.url,
                  isThumbnail: img.metadata?.isThumbnail || false,
                  field_id: img.id,
                }));

                setFormData({
                  name: destinationData.name || "",
                  handle: destinationData.handle || "",
                  description: destinationData.description || "",
                  is_active: destinationData.is_active ?? true,
                  is_featured: destinationData.is_featured ?? false,
                  country: destinationData.country || "",
                  currency: destinationData.currency || "",
                  location: destinationData.location || null,
                  tags: destinationData.tags || null,
                  website: destinationData.website || null,
                  category_id: destinationData.category_id || "",
                  media: mediaItems,
                });
              }
            } catch (imageError) {
              console.error("Error fetching destination images:", imageError);
              // Still set form data even if images fail to load
              setFormData({
                name: destinationData.name || "",
                handle: destinationData.handle || "",
                description: destinationData.description || "",
                is_active: destinationData.is_active ?? true,
                is_featured: destinationData.is_featured ?? false,
                country: destinationData.country || "",
                currency: destinationData.currency || "",
                location: destinationData.location || null,
                tags: destinationData.tags || null,
                website: destinationData.website || null,
                category_id: destinationData.category_id || "",
                media: [],
              });
            }
          }
        } else {
          // Handle case when destination data is not found
          console.error("Destination not found for slug:", slug);
          toast.error("Error", {
            description: "Destination not found",
          });
        }
      } catch (error) {
        console.error("Error fetching destination:", error);
        toast.error("Error", {
          description: "Failed to load destination",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDestinationData();
  }, [slug]);

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!destination) {
    return (
      <>
        <HideSidebarItemsWidget />
        <Container className="py-8">
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <Text className="text-gray-500 mb-4">
            Destination not found
          </Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate("/hotel-management/destinations")}
          >
            Back to Destinations
          </Button>
        </div>
      </Container>
      </>
    );
  }

  const handleUpdate = async (updatedData?: DestinationFormData) => {
    // Use the updated data if provided, otherwise use the current formData
    const dataToUse = updatedData || formData;
    try {
      // First update the destination basic info
      // Ensure tags is properly formatted
      let formattedTags = dataToUse.tags;
      console.log('Data in handle update', dataToUse);
      if (typeof dataToUse.tags === 'string') {
        try {
          // Try to parse if it's a JSON string
          formattedTags = JSON.parse(dataToUse.tags as string);
        } catch (e) {
          // If not a valid JSON, split by comma
          formattedTags = (dataToUse.tags as string).split(',').map(tag => tag.trim());
        }
      }

      const destinationData = {
        name: dataToUse.name,
        handle: dataToUse.handle,
        description: dataToUse.description,
        is_active: dataToUse.is_active,
        is_featured: dataToUse.is_featured,
        country: dataToUse.country,
        currency: dataToUse.currency,
        location: dataToUse.location,
        tags: formattedTags,
        website: dataToUse.website,
        category_id: dataToUse.category_id,
        id: destination?.id,
      };

      const response = await fetch(`/admin/hotel-management/destinations`, {
        method: "PUT",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(destinationData),
      });

      const data = await response.json();

      if (response.ok) {
        // Handle image uploads for new images
        if (dataToUse.media && dataToUse.media.length > 0) {
          const destinationId = destination?.id;

          // Upload each new image (ones with file property)
          for (const media of dataToUse.media) {
            if (media.file) {
              const formData = new FormData();
              formData.append("files", media.file);

              // Add metadata including thumbnail flag
              const metadata = {
                isThumbnail: media.isThumbnail,
              };
              formData.append("metadata", JSON.stringify(metadata));

              try {
                const uploadResponse = await fetch(
                  `/admin/hotel-management/destinations/${destinationId}/upload`,
                  {
                    method: "POST",
                    credentials: "include",
                    body: formData,
                  }
                );

                if (!uploadResponse.ok) {
                  console.error("Failed to upload image", await uploadResponse.text());
                }
              } catch (uploadError) {
                console.error("Error uploading image:", uploadError);
              }
            }
          }
        }

        toast.success("Success", {
          description: "Destination updated successfully",
        });

        // Refresh the destination data
        const refreshResponse = await fetch(`/admin/hotel-management/destinations/${slug}`, {
          credentials: "include",
        });
        const refreshData = await refreshResponse.json();
        const destinationData = Array.isArray(refreshData?.destination)
          ? refreshData?.destination[0]
          : refreshData.destination;

        if (destinationData) {
          setDestination(destinationData);

          // Refresh images
          const imagesResponse = await fetch(
            `/admin/hotel-management/destinations/${destinationData.id}/images`,
            { credentials: "include" }
          );
          const imagesData = await imagesResponse.json();

          if (imagesData.images) {
            setDestinationImages(imagesData.images);
          }
        }

        return true;
      } else {
        toast.error("Error", {
          description: data.message || "Failed to update destination",
        });
        return false;
      }
    } catch (error) {
      console.error("Error updating destination:", error);
      toast.error("Error", {
        description: "An unexpected error occurred",
      });
      return false;
    }
  };

  const handleDelete = async () => {
    try {
      const response = await fetch(`/admin/hotel-management/destinations`, {
        method: "DELETE",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ids: destination?.id,
        }),
      });

      if (response.ok) {
        toast.success("Success", {
          description: "Destination deleted successfully",
        });
        navigate("/hotel-management/destinations");
      } else {
        const data = await response.json();
        toast.error("Error", {
          description: data.message || "Failed to delete destination",
        });
      }
    } catch (error) {
      toast.error("Error", {
        description: "An unexpected error occurred",
      });
    }
  };

  // Filter for active hotels
  const activeHotels = hotels.filter(hotel => hotel.is_active);

  // Find a hero image for the destination
  const heroImage = destinationImages.length > 0
    ? destinationImages.find(img => img.metadata?.isThumbnail)?.url || destinationImages[0].url
    : null;

  return (
    <>
      <HideSidebarItemsWidget />
      <Toaster />
      <div className="flex flex-col gap-6">
        {/* Back button */}
        <div className="px-4">
          <Button
            variant="secondary"
            size="small"
            className="flex items-center gap-1"
            onClick={() => navigate('/hotel-management/destinations')}
          >
            <ChevronLeft className="w-4 h-4" />
            Back to Destinations
          </Button>
        </div>

        {/* Hero section with image background */}
        <div className="relative w-full h-64 overflow-hidden rounded-lg shadow-md">
          {/* Background image with gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-b from-gray-900/70 to-gray-900/30 z-1"></div>
          {heroImage ? (
            <img
              src={heroImage}
              alt={destination?.name}
              className="absolute inset-0 w-full h-full object-cover"
            />
          ) : (
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600"></div>
          )}

          {/* Content overlay */}
          <div className="relative z-2 h-full flex flex-col justify-between p-6">
            <div className="flex justify-between items-start">
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  <Badge color={destination?.is_active ? "green" : "grey"} className="text-xs relative z-1">
                    {destination?.is_active ? "Active" : "Inactive"}
                  </Badge>
                  {destination?.is_featured && (
                    <Badge color="blue" className="text-xs relative z-1">Featured</Badge>
                  )}
                </div>
                <Heading level="h1" className="text-3xl font-bold text-white">
                  {destination?.name}
                </Heading>
                <div className="flex items-center gap-2 text-white/80">
                  <MapPin className="w-4 h-4" />
                  <Text className="text-white/90">
                    {destination?.location || destination?.country || "Location not specified"}
                  </Text>
                </div>
              </div>

              <div className="flex gap-x-2 relative z-1">
                <FocusModal open={open} onOpenChange={setOpen}>
                  <FocusModal.Trigger asChild>
                    <Button variant="secondary" size="small" className="bg-white/90 hover:bg-white relative z-1">
                      <Edit className="w-4 h-4 mr-1" /> Edit
                    </Button>
                  </FocusModal.Trigger>
                  <DestinationFormModern
                    formData={formData as any}
                    setFormData={setFormData}
                    onSubmit={handleUpdate}
                    closeModal={() => setOpen(false)}
                    isEdit
                  />
                </FocusModal>
                <Prompt
                  open={deleteOpen}
                  onOpenChange={setDeleteOpen}
                  title="Delete Destination"
                  description="Are you sure you want to delete this destination? This action cannot be undone."
                  onDelete={handleDelete}
                  trigger={
                    <Button variant="danger" size="small" className="bg-red-500/90 hover:bg-red-600 relative z-1">
                      <Trash2 className="w-4 h-4 mr-1" /> Delete
                    </Button>
                  }
                />
              </div>
            </div>

            <div className="flex flex-wrap gap-4 mt-auto relative z-1">
              <div className="bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full flex items-center gap-2 relative z-1">
                <Globe className="w-4 h-4 text-white" />
                <Text className="text-white font-medium">{destination?.country || "Country not specified"}</Text>
              </div>

              <div className="bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full flex items-center gap-2 relative z-1">
                <DollarSign className="w-4 h-4 text-white" />
                <Text className="text-white font-medium">{destination?.currency || "Currency not specified"}</Text>
              </div>

              <div className="bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full flex items-center gap-2 relative z-1">
                <Buildings className="w-4 h-4 text-white" />
                <Text className="text-white font-medium">{activeHotels.length} Active Hotels</Text>
              </div>
            </div>
          </div>
        </div>

        {/* Main content container */}
        <Container className="mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Details Section */}
            <div>
              <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b flex items-center gap-2 bg-gray-50">
                  <FileText className="w-5 h-5 text-gray-600" />
                  <Heading level="h2" className="text-lg font-medium">
                    Destination Details
                  </Heading>
                </div>
                <div className="p-6 space-y-6">
                  {/* Description */}
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <Text className="text-sm text-gray-700 leading-relaxed">
                      {destination?.description || "No description available for this destination."}
                    </Text>
                  </div>

                  {/* Details with icons */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0">
                        <Hash className="w-5 h-5 text-purple-600" />
                      </div>
                      <div>
                        <Text className="text-xs text-gray-500">Handle</Text>
                        <Text className="font-medium">{destination?.handle}</Text>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                        <Globe className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <Text className="text-xs text-gray-500">Country</Text>
                        <Text className="font-medium">{destination?.country || "Not specified"}</Text>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center flex-shrink-0">
                        <DollarSign className="w-5 h-5 text-yellow-600" />
                      </div>
                      <div>
                        <Text className="text-xs text-gray-500">Currency</Text>
                        <Text className="font-medium">{destination?.currency || "Not specified"}</Text>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center flex-shrink-0">
                        <MapPin className="w-5 h-5 text-red-600" />
                      </div>
                      <div>
                        <Text className="text-xs text-gray-500">Location</Text>
                        <Text className="font-medium">{destination?.location || "Not specified"}</Text>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                        <Bookmark className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <Text className="text-xs text-gray-500">Featured</Text>
                        <div className="flex items-center gap-1">
                          {destination?.is_featured ? (
                            <>
                              <CheckCircle className="w-4 h-4 text-green-500" />
                              <Text className="font-medium">Yes</Text>
                            </>
                          ) : (
                            <>
                              <XCircle className="w-4 h-4 text-gray-400" />
                              <Text className="font-medium">No</Text>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center flex-shrink-0">
                        <Flag className="w-5 h-5 text-indigo-600" />
                      </div>
                      <div>
                        <Text className="text-xs text-gray-500">Status</Text>
                        <div className="flex items-center gap-1">
                          {destination?.is_active ? (
                            <>
                              <CheckCircle className="w-4 h-4 text-green-500" />
                              <Text className="font-medium">Active</Text>
                            </>
                          ) : (
                            <>
                              <XCircle className="w-4 h-4 text-red-500" />
                              <Text className="font-medium">Inactive</Text>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Tags */}
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <Tag className="w-5 h-5 text-gray-600" />
                      <Text className="font-medium">Tags</Text>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {(() => {
                        // Handle different formats of tags
                        let tagsArray: string[] = [];

                        if (destination?.tags) {
                          if (Array.isArray(destination.tags)) {
                            tagsArray = destination.tags;
                          } else if (typeof destination.tags === 'string') {
                            try {
                              // Try to parse JSON string
                              const parsed = JSON.parse(destination.tags as string);
                              tagsArray = Array.isArray(parsed) ? parsed : [];
                            } catch (e) {
                              // If not valid JSON, try comma-separated
                              tagsArray = (destination.tags as string).split(',').map((t: string) => t.trim());
                            }
                          } else if (typeof destination.tags === 'object') {
                            // Handle case where tags might be an object
                            tagsArray = Object.values(destination.tags);
                          }
                        }

                        return tagsArray.length > 0 ? tagsArray.map(tag => (
                          <span
                            key={tag}
                            className="px-3 py-1 bg-gray-100 text-gray-700 text-xs rounded-full hover:bg-gray-200 transition-colors"
                          >
                            {tag}
                          </span>
                        )) : <Text className="text-sm text-gray-500">No tags available</Text>;
                      })()}
                    </div>
                  </div>
                </div>
              </Container>

              {/* Images Section */}
              <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 mt-6 rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b flex items-center gap-2 bg-gray-50">
                  <Image className="w-5 h-5 text-gray-600" />
                  <Heading level="h2" className="text-lg font-medium">
                    Destination Images
                  </Heading>
                </div>
                <div className="p-6">
                  {destinationImages.length > 0 ? (
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {destinationImages.map((image) => (
                        <div key={image.id} className="relative group overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-all">
                          <img
                            src={image.url}
                            alt={destination?.name}
                            className="w-full h-40 object-cover"
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300"></div>
                          {image.metadata?.isThumbnail && (
                            <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-3 py-1 rounded-full shadow-sm">
                              Featured Image
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
                      <Image className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                      <Text className="text-gray-500">No images available</Text>
                      <Text className="text-gray-400 text-sm">Images can be added when editing the destination</Text>
                    </div>
                  )}
                </div>
              </Container>

            </div>

            {/* Right Column */}
            <div>
              {/* Hotels Section */}
              <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b flex justify-between items-center bg-gray-50">
                  <div className="flex items-center gap-2">
                    <Hotel className="w-5 h-5 text-gray-600" />
                    <Heading level="h2" className="text-lg font-medium">
                      Hotels in {destination?.name}
                    </Heading>
                  </div>
                  <Link
                    to="/hotel-management/hotels/"
                    className="flex items-center gap-1 px-3 py-1 text-white rounded-md transition-colors text-sm"
                  >
                    <PlusMini className="w-4 h-4" />
                    <span>Add Hotel</span>
                  </Link>
                </div>

                {/* Hotel search and filter */}
                <div className="px-6 py-3 border-b bg-gray-50/50 flex items-center gap-3">
                  <div className="relative flex-grow">
                    <input
                      type="text"
                      placeholder="Search hotels..."
                      className="w-full pl-9 pr-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <MagnifyingGlass className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  </div>
                  <button className="flex items-center gap-1 px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                    <Filter className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-700">Filter</span>
                  </button>
                </div>

                <div className="p-6">
                  {isLoadingHotels ? (
                    <div className="flex justify-center py-8">
                      <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                  ) : hotels.length > 0 ? (
                    <div className="space-y-4">
                      {hotels.map((hotel) => (
                        <div
                          key={hotel.id}
                          className="overflow-hidden hover:shadow-md transition-all border border-gray-200 rounded-lg bg-white flex flex-col md:flex-row h-auto md:h-48 relative z-1"
                        >
                          <div className="w-full md:w-1/3 h-48 md:h-full relative overflow-hidden">
                            {hotel.images && hotel.images.length > 0 ? (
                              <img
                                src={hotel.images[0].url}
                                alt={hotel.name}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full bg-gradient-to-r from-blue-100 to-blue-50 flex items-center justify-center">
                                <Buildings className="w-12 h-12 text-blue-300" />
                              </div>
                            )}
                            <Badge
                              color={hotel.is_active ? "green" : "grey"}
                              className="absolute top-2 right-2 shadow-sm z-1"
                            >
                              {hotel.is_active ? "Active" : "Inactive"}
                            </Badge>
                          </div>

                          <div className="w-full md:w-2/3 p-5 flex flex-col">
                            <div className="flex justify-between items-start mb-2">
                              <Link
                                to={`/hotel-management/hotels/${hotel.id}`}
                                className="text-xl font-medium text-blue-600 hover:text-blue-800 hover:underline"
                              >
                                {hotel.name}
                              </Link>
                            </div>

                            <div className="flex flex-wrap gap-x-4 gap-y-2 mb-3">
                              {hotel.rating && (
                                <div className="flex items-center gap-1">
                                  <div className="flex">
                                    {Array.from({ length: 5 }).map((_, i) => (
                                      <Star
                                        key={i}
                                        className={`w-4 h-4 ${i < (hotel.rating || 0) ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
                                      />
                                    ))}
                                  </div>
                                  <span className="text-sm text-gray-600">
                                    ({hotel.total_reviews || 0} reviews)
                                  </span>
                                </div>
                              )}

                              {hotel.location && (
                                <div className="flex items-center gap-1 text-gray-600 text-sm">
                                  <MapPin className="w-4 h-4 text-gray-500" />
                                  <span>{hotel.location}</span>
                                </div>
                              )}

                              {hotel.website && (
                                <div className="flex items-center gap-1 text-gray-600 text-sm">
                                  <Globe className="w-4 h-4 text-gray-500" />
                                  <a href={hotel.website} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline truncate">
                                    {hotel.website.replace(/^https?:\/\//, '')}
                                  </a>
                                </div>
                              )}
                            </div>

                            <Text className="text-sm text-gray-600 line-clamp-2 mb-4 flex-grow">
                              {hotel.description || "No description available"}
                            </Text>

                            <div className="flex gap-2 mt-auto">
                              <Button
                                variant="secondary"
                                size="small"
                                onClick={() => navigate(`/hotel-management/hotels/${hotel.id}`)}
                                className="flex-1 flex items-center justify-center gap-1"
                              >
                                <Edit className="w-4 h-4" /> Manage
                              </Button>
                              <Button
                                variant="secondary"
                                size="small"
                                onClick={() => navigate(`/hotel-management/hotels/${hotel.id}/rooms`)}
                                className="flex-1 flex items-center justify-center gap-1"
                              >
                                <Bed className="w-4 h-4" /> Rooms
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12 bg-gray-50 rounded-lg border border-gray-200">
                      <Hotel className="w-16 h-16 text-gray-300 mx-auto mb-3" />
                      <Text className="text-gray-500 mb-4 font-medium">No hotels available for this destination</Text>
                      <Text className="text-gray-400 text-sm mb-6">Add hotels to this destination to manage them here</Text>
                      <Link
                        to="/hotel-management/hotels/new"
                        className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                      >
                        <PlusMini className="w-4 h-4 mr-2" />
                        Add a Hotel
                      </Link>
                    </div>
                  )}
                </div>
              </Container>

              {/* System Information */}
              <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 mt-6 rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b flex items-center gap-2 bg-gray-50">
                  <Clock className="w-5 h-5 text-gray-600" />
                  <Heading level="h2" className="text-lg font-medium">
                    System Information
                  </Heading>
                </div>
                <div className="p-6 space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center flex-shrink-0">
                      <Clock className="w-5 h-5 text-gray-600" />
                    </div>
                    <div>
                      <Text className="text-xs text-gray-500">Created At</Text>
                      <Text className="font-medium">{new Date(destination?.created_at || '').toLocaleString()}</Text>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center flex-shrink-0">
                      <Clock className="w-5 h-5 text-gray-600" />
                    </div>
                    <div>
                      <Text className="text-xs text-gray-500">Updated At</Text>
                      <Text className="font-medium">{new Date(destination?.updated_at || '').toLocaleString()}</Text>
                    </div>
                  </div>
                </div>
              </Container>

              {/* Metafields Widget */}
              <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 mt-6 rounded-lg overflow-hidden">
                <DestinationMetafieldsWidget />
              </Container>
            </div>
          </div>
        </Container>
      </div>
    </>
  );
};

export default DestinationDetailPage;
