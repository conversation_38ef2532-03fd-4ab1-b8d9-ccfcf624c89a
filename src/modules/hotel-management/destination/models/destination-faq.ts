import { model } from "@camped-ai/framework/utils";
import { Destination } from "./destination";

export const DestinationFaq = model
  .define(
    { tableName: "destination_faq", name: "DestinationFaq" },
    {
      id: model.id({ prefix: "dest_faq" }).primaryKey(),
      question: model.text(),
      answer: model.text(),
      rank: model.number().default(0),
      is_active: model.boolean().default(true),
      destination: model.belongsTo(() => Destination, {
        mappedBy: "faqs",
      }),
    }
  )
  .indexes([
    {
      name: "IDX_Destination_faq_destination_id",
      on: ["destination_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_Destination_faq_rank",
      on: ["destination_id", "rank"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);
