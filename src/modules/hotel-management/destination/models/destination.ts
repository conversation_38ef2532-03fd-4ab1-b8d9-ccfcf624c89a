import { model } from "@camped-ai/framework/utils";
import { DestinationImage } from "./destination-image";
import { DestinationFaq } from "./destination-faq";

export const Destination = model.define("destination", {
  id: model.id().primaryKey(),
  category_id: model.text(),
  name: model.text(),
  handle: model.text(),
  description: model.text().nullable(),
  is_active: model.boolean().default(true),
  country: model.text(),
  currency: model.text(),
  location: model.text().nullable(),
  tags: model.json().nullable(),
  website: model.text().nullable(),
  is_featured: model.boolean().default(false),
  images: model.hasMany(() => DestinationImage, {
      foreignKey: "destination_id",
      localKey: "id",
    }),
});
